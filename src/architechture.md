# Local File Research System – Comprehensive Architecture Guide

## 🏗️ System Overview

The Local File Research System is a sophisticated, multi-layered platform designed for deep document analysis, semantic search, and collaborative research. Built with a microservices architecture, it combines FastAPI for robust backend services, Streamlit for interactive user interfaces, and advanced AI capabilities through DSPy and LlamaIndex integration.

### 🎯 Core Philosophy
- **Local-First**: All processing happens on your machine - no cloud dependencies
- **Multi-User**: Comprehensive authentication and collaboration features
- **Multi-Vectorstore**: Support for multiple vector stores and embedding models
- **Extensible**: Modular design for easy feature additions and customizations

---

## 🚀 System Startup & Entry Points

### Main Entry Point
```bash
python -m local_file_research.main_llamaindex both
```

This command triggers the complete system initialization through `main_llamaindex.py`, supporting multiple modes:
- `api` - API server only
- `ui` - UI server only
- `both` - Full system (API + UI + Auth UI)
- `test` - Run test suite
- `migrate` - Database migration utilities

### Startup Sequence (`run_both()`)

#### 1. System Preparation
- **Configuration Loading**: Imports settings from `config.py` including ports, API keys, and feature flags
- **Directory Structure**: Creates required directories (`project_indices/`, `sessions/`, `storage/`)
- **Cleanup Operations**:
  - Embeddings cache cleanup via `database_cleanup.cleanup_embeddings_directory`
  - Storage content cleanup via `document_cleanup.cleanup_storage_files`
  - Project storage cleanup via `document_cleanup.cleanup_projects_folder`

#### 2. Service Orchestration
- **API Server** (Port 8006): FastAPI backend with comprehensive REST endpoints
- **Main UI** (Port 8501): Streamlit interface for research and document management
- **Auth UI** (Port 8502): Dedicated authentication and security management interface
- **Health Checks**: Automated service readiness verification with retry logic

---

## 🏛️ Multi-Layered Architecture

### Layer 1: Core Infrastructure
The foundation layer provides essential services and utilities:

#### Configuration Management (`config.py`)
- **Environment Variables**: API keys, ports, feature flags
- **Model Settings**: Embedding dimensions, FAISS index types, LLM configurations
- **Security Settings**: CORS policies, authentication requirements
- **Research Modes**: RAG, multi-iteration, chain-of-thought configurations
- **Report Types**: Normal, enhanced, comprehensive article generation

#### System Maintenance
- **Database Cleanup** (`database_cleanup.py`): Analytics DB, embeddings cache management
- **Document Cleanup** (`document_cleanup.py`): Storage optimization, orphaned file removal
- **LLM Patches** (`litellm_patch.py`): Compatibility fixes for various LLM providers

### Layer 2: Data & Storage Management

#### Document Processing Pipeline
- **File Indexer** (`file_indexer.py`): Multi-format document ingestion (PDF, DOCX, CSV, JSON, HTML, etc.)
- **Document Processor** (`document_processor.py`): Advanced text extraction with metadata preservation
- **Document Manager** (`document_manager.py`): CRUD operations, versioning, access control
- **Storage Manager** (`storage_manager.py`): File system abstraction with project isolation

#### Vector Store Infrastructure
- **LlamaIndex Integration** (`llamaindex_vector_store.py`):
  - FAISS backend with HNSW, IVF, and Flat index support
  - QueryFusionRetriever for hybrid vector + BM25 search
  - Persistent storage with automatic index optimization
- **Legacy Vector Store** (`vector_store.py`): Fallback implementation
- **Embedding System** (`embedding.py`):
  - Ollama integration for local embeddings
  - Sentence-transformers support
  - Caching and batch processing

### Layer 3: API Services (`api_llamaindex_main.py`)

#### Core Research API (`api_llamaindex.py`)
- **Document Indexing**: Batch processing with progress tracking
- **Semantic Search**: Multi-modal retrieval with relevance scoring
- **Deep Research**: Multi-iteration analysis with context accumulation

#### Specialized API Endpoints
- **Document Management** (`api_documents.py`):
  - Upload/download with metadata extraction
  - Bulk operations and batch processing
  - Content analysis and summarization
- **Project Management** (`api_projects.py`):
  - Multi-user project creation and management
  - Document organization and sharing
  - Access control and permissions
- **Session Management** (`api_sessions.py`):
  - Research session persistence
  - Context preservation across queries
  - Session sharing and collaboration
- **Authentication** (`api_auth.py`):
  - JWT-based authentication
  - Two-factor authentication (2FA) support
  - Role-based access control
- **Administration** (`api_admin.py`):
  - System monitoring and metrics
  - User management and analytics
  - System configuration and maintenance

### Layer 4: User Interface & Experience

#### Main Research Interface (`ui_llamaindex.py`)
- **Research Dashboard**: Multi-mode research with real-time results
- **Document Management**: Upload, organize, and analyze documents
- **Project Workspace**: Collaborative project management
- **Vector Store Management**: Multiple vector store configuration
- **Analytics Dashboard**: Usage metrics and performance insights
- **Settings & Configuration**: System customization and preferences

#### Authentication Interface (`auth_app.py`)
- **User Registration**: Account creation with email verification
- **Login System**: Secure authentication with 2FA support
- **Security Settings**: Password management, 2FA setup
- **Account Management**: Profile updates, security preferences

#### Collaboration Features
- **Comment System** (`comment_manager.py`): Document annotations and discussions
- **Real-time Collaboration** (`collaboration.py`): Multi-user project sharing
- **Team Management**: User roles, permissions, and access control

---

## 🧠 AI & Research Intelligence

### DSPy Agent System (`dspy_config.py`, `dspy_agents.py`)

#### Core Analysis Agents
- **Summarizer**: Intelligent document summarization with key point extraction
- **Answerer**: Context-aware question answering with source attribution
- **Extractor**: Structured data extraction from unstructured content
- **Chain of Thought**: Step-by-step reasoning for complex queries
- **Fact Checker**: Information verification and source validation

#### Specialized Document Agents
- **Code Analyzer**: Programming language detection, complexity analysis, documentation generation
- **Spreadsheet Analyzer**: Data pattern recognition, statistical analysis, trend identification
- **PDF Analyzer**: Layout-aware text extraction, table processing, image analysis
- **Technical Document Analyzer**: API documentation, technical specification analysis
- **Research Paper Analyzer**: Academic paper structure analysis, citation extraction

#### Advanced Research Agents
- **Interpreter**: Deep meaning analysis and context interpretation
- **Proposal Generator**: Actionable recommendations and strategic insights
- **Technical Analyzer**: Technical feasibility and implementation analysis
- **Content Synthesizer**: Multi-source information synthesis
- **Multi-Document Synthesizer**: Cross-document pattern recognition and synthesis

### Research System (`research_system.py`)

#### Research Modes
- **RAG (Retrieval-Augmented Generation)**: Standard semantic search with AI enhancement
- **Multi-Iteration Research** (`multi_iteration_research.py`):
  - Iterative query refinement and expansion
  - Context accumulation across search iterations
  - Follow-up question generation and exploration
  - Dynamic result ranking and deduplication

#### Advanced Reporting (`advanced_reporting.py`)
- **Report Types**:
  - **Normal**: Standard findings with basic analysis
  - **Chain of Thought**: Step-by-step reasoning documentation
  - **Enhanced**: Comprehensive analysis with interpretations, proposals, and technical views
- **Article Generation**:
  - Informative articles, analytical reports, technical documentation
  - Research summaries, comparative analyses, strategic recommendations
  - Automatic article type detection based on content analysis

---

## 📊 Analytics & Monitoring System

### Analytics Engine (`analytics.py`)
- **Usage Tracking**: User activity, feature utilization, performance metrics
- **Performance Monitoring**: Query response times, indexing performance, system resource usage
- **Event Logging**: User actions, system events, error tracking
- **Metrics Collection**: Custom metrics, KPIs, trend analysis

### Analytics Dashboard (`analytics_dashboard.py`)
- **Real-time Metrics**: Live system performance and usage statistics
- **User Activity**: Login patterns, feature usage, collaboration metrics
- **Document Analytics**: Processing statistics, search patterns, popular content
- **Performance Insights**: Response times, throughput, resource utilization
- **Export Capabilities**: Data export, report generation, trend analysis

---

## 🔐 Security & Authentication

### Authentication System (`auth.py`)
- **User Management**: Registration, login, password management
- **JWT Tokens**: Secure token-based authentication with expiration
- **Two-Factor Authentication**: TOTP-based 2FA with QR code generation
- **Session Management**: Secure session handling and cleanup
- **Role-Based Access**: User roles and permission management

### Security Features
- **API Key Protection**: Optional API key authentication for enhanced security
- **CORS Configuration**: Configurable cross-origin resource sharing
- **Input Validation**: Comprehensive input sanitization and validation
- **Audit Logging**: Security event logging and monitoring

---

## 🤝 Multi-User & Collaboration Features

### Project Management
- **Multi-User Projects**: Shared workspaces with role-based access
- **Document Sharing**: Granular sharing permissions (read/write)
- **Team Collaboration**: Real-time collaboration on research projects
- **Access Control**: Owner, member, and viewer roles with appropriate permissions

### Collaboration Tools
- **Comment System**: Document-level and project-level discussions
- **Real-time Updates**: Live collaboration with conflict resolution
- **Activity Feeds**: Project activity tracking and notifications
- **Version Control**: Document versioning and change tracking

---

## 🗄️ Multi-Vectorstore Architecture

### Vector Store Support
- **Primary**: LlamaIndex with FAISS backend
  - **Index Types**: HNSW (Hierarchical Navigable Small World), IVF (Inverted File), Flat
  - **Metrics**: Inner Product (cosine similarity), L2 (Euclidean distance)
  - **Persistence**: Automatic saving and loading with optimization
- **Hybrid Search**: QueryFusionRetriever combining vector and BM25 search
- **Fallback**: Legacy vector store implementation for compatibility

### Embedding Models
- **Local Models**: Ollama integration (mxbai-embed-large, nomic-embed-text)
- **Cloud Models**: Sentence-transformers, OpenAI embeddings
- **Configurable Dimensions**: Support for various embedding dimensions (384, 768, 1024, 1536)
- **Caching**: Intelligent embedding caching for performance optimization

---

## 📈 System Dependencies & Data Flow

### Core Data Flow Patterns

#### Research Query Flow
```
User Query → UI → API → Research System → Vector Store → DSPy Agents → Results
```

#### Document Processing Flow
```
File Upload → Document Processor → Embedding Generation → Vector Store → Index Update
```

#### Multi-User Collaboration Flow
```
User Action → Authentication → Authorization → Project Access → Collaboration Engine → Real-time Updates
```

### Dependency Hierarchy

```
🏗️ main_llamaindex.py (Entry Point)
├── 🔧 System Infrastructure
│   ├── config.py (Configuration Management)
│   ├── litellm_patch.py (LLM Compatibility)
│   ├── database_cleanup.py (DB Maintenance)
│   └── document_cleanup.py (Storage Cleanup)
├── 🌐 API Layer (api_llamaindex_main.py)
│   ├── 🔍 Core Research (api_llamaindex.py)
│   │   ├── pipeline_llamaindex.py (Research Pipeline)
│   │   ├── document_manager.py (Document CRUD)
│   │   └── embedding.py (Embedding Generation)
│   ├── 📄 Document API (api_documents.py)
│   ├── 📁 Project API (api_projects.py)
│   ├── 🔐 Auth API (api_auth.py)
│   ├── ⚙️ Admin API (api_admin.py)
│   └── 📊 Session API (api_sessions.py)
├── 🖥️ User Interface (ui_llamaindex.py)
│   ├── 🔐 Authentication UI (auth_ui.py)
│   ├── 💬 Collaboration (collaboration.py)
│   ├── 📝 Comments (comment_manager.py)
│   ├── 📊 Analytics (analytics_dashboard.py)
│   └── 📄 Document Processing (document_processor.py)
├── 🔐 Authentication App (auth_app.py)
│   ├── auth.py (Auth Logic)
│   └── auth_ui.py (Auth Interface)
├── 🧠 AI Research System (research_system.py)
│   ├── 🔄 Multi-Iteration Research (multi_iteration_research.py)
│   ├── 📊 Advanced Reporting (advanced_reporting.py)
│   └── 🤖 DSPy Configuration (dspy_config.py)
│       └── dspy_agents.py (AI Agents)
├── 🗄️ Vector Storage
│   ├── llamaindex_vector_store.py (Primary Vector Store)
│   └── vector_store.py (Legacy Support)
├── 📊 Analytics & Monitoring
│   ├── analytics.py (Analytics Engine)
│   └── system_metrics.py (Performance Metrics)
└── 🛠️ Utilities & Tools
    ├── file_indexer.py (File Processing)
    ├── storage_manager.py (Storage Abstraction)
    ├── models.py (Data Models)
    └── serialization_utils.py (Data Serialization)
```

---

## 🔧 System Configuration & Customization

### Environment Configuration
- **API Ports**: Configurable ports for API (8006), UI (8501), Auth UI (8502)
- **Model Settings**: Embedding model selection, dimension configuration
- **Feature Flags**: Enable/disable specific features (2FA, analytics, collaboration)
- **Performance Tuning**: Vector store optimization, caching settings

### Extensibility Points
- **Custom DSPy Agents**: Add specialized analysis agents for domain-specific tasks
- **Document Processors**: Support for additional file formats and data sources
- **Vector Store Backends**: Plugin architecture for alternative vector stores
- **Authentication Providers**: Integration with external auth systems (LDAP, OAuth)

---

## 📋 Module Status & Maintenance

### Active Core Modules
All modules in the system are actively used and maintained:

| Module Category | Status | Purpose |
|----------------|--------|---------|
| **Core Infrastructure** | ✅ Active | System foundation and configuration |
| **API Services** | ✅ Active | REST API endpoints and business logic |
| **User Interfaces** | ✅ Active | Web-based user interaction |
| **AI & Research** | ✅ Active | Intelligent analysis and research capabilities |
| **Data Management** | ✅ Active | Document processing and storage |
| **Security & Auth** | ✅ Active | User management and security |
| **Analytics** | ✅ Active | System monitoring and insights |
| **Collaboration** | ✅ Active | Multi-user features and sharing |

### Testing & Quality Assurance
- **Unit Tests**: Comprehensive test coverage for core functionality
- **Integration Tests**: End-to-end testing of API and UI workflows
- **Performance Tests**: Load testing and performance benchmarking
- **Security Tests**: Authentication and authorization validation

---