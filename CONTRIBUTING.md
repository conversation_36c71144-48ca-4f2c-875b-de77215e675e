# Contributing to Local File Research System

Thank you for your interest in contributing!

## How to Contribute

- **Bug Reports & Feature Requests:**
  - Please use GitHub Issues to report bugs or request features.

- **Pull Requests:**
  1. Fork the repository and create your branch from `main`.
  2. Add your changes with clear, descriptive commit messages.
  3. Ensure your code passes all tests and lint checks.
  4. Submit a pull request with a description of your changes.

- **Code Style:**
  - Follow PEP8 for Python code.
  - Add docstrings and comments where helpful.

- **Tests:**
  - Add or update tests for new features and bug fixes.

## Community
- Be respectful and constructive in all interactions.
- For major changes, please open an issue first to discuss what you would like to change.
