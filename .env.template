# Local File Research - Environment Configuration
# This file contains all configurable parameters for the application

# ===== API Configuration =====
API_HOST=0.0.0.0
API_PORT=8006

# ===== Session Configuration =====
SESSION_PERSIST_DIR=sessions

# ===== Vector Store Configuration =====
USE_FAISS=true
FAISS_INDEX_TYPE=hnsw
CHUNK_SIZE=1024
CHUNK_OVERLAP=0

# ===== DSPy Configuration =====
DSPY_LLM_PROVIDER=openai
DSPY_LLM_MODEL=gpt-4.1-2025-04-14
DSPY_TEMPERATURE=0.4
DSPY_MAX_TOKENS=8192
DSPY_API_BASE=

# ===== API Keys =====
OPENAI_API_KEY=
ANTHROPIC_API_KEY=

# ===== Embedding Configuration =====
EMBEDDING_MODEL_NAME=mxbai-embed-large:latest
EMBEDDING_DIMENSION=1024
OLLAMA_API_BASE=http://localhost:11434
EMBEDDING_CACHE_DIR=embeddings

# ===== Logging Configuration =====
LOG_LEVEL=INFO
LOG_FILE=local_file_research.log

# ===== Document Analysis Configuration =====
DEFAULT_DOCUMENT_ANALYSIS_MODE=summarize

# ===== Research System Configuration =====
DEFAULT_RESEARCH_MODE=rag
DEFAULT_REPORT_MODE=normal

# ===== Security Configuration =====
API_KEY=
ENABLE_CORS=true
CORS_ORIGINS=*

# ===== Authentication Configuration =====
AUTH_DIR=auth_data
JWT_SECRET=
JWT_ALGORITHM=HS256
JWT_EXPIRY_DAYS=7
PASSWORD_SALT_SIZE=16
MIN_PASSWORD_LENGTH=8

# ===== Directory Configuration =====
BASE_DIR=
STORAGE_DIR=storage
VERSIONS_DIR=versions
COLLAB_DIR=collaboration
EXPORTS_DIR=exports
SECURITY_DIR=security
ANALYTICS_DIR=analytics

# Legacy directories (kept for backward compatibility)
DATABASE_DIR=database
DOCUMENTS_DIR=documents

# ===== Analytics Configuration =====
TRACK_ANALYTICS=true

# ===== Testing Configuration =====
TEST_TIMEOUT=10
MAX_WORKERS=4
API_URL=http://localhost:8006
